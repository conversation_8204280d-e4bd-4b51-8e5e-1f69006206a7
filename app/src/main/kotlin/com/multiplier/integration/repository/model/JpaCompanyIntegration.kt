package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.types.PlatformCategory
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(
    name = "company_integration",
    schema = "customer_integration",
)
class JpaCompanyIntegration(
    id: Long? = null,
    @Column(name = "company_id") val companyId: Long,
    @ManyToOne(cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    @JoinColumn(name = "provider_id", nullable = false)
    val provider: JpaProvider,
    @ManyToOne(cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    @JoinColumn(name = "platform_id", nullable = false)
    val platform: JpaPlatform,
    @Column(name = "account_token") var accountToken: String,
    @Column(name = "enabled") var enabled: Boolean,
    @Column(name = "outgoing_sync_enabled") var outgoingSyncEnabled: Boolean = false,
    @Column(name = "incoming_sync_enabled") var incomingSyncEnabled: Boolean = false,
    @Column(name = "timeoff_sync_enabled") var timeOffSyncEnabled: Boolean = false,
    @Column(name = "incoming_sync_in_progress") var incomingSyncInProgress: Boolean = false,
    @Column(name = "import_in_progress") var importInProgress: Boolean = false,
    @Column(name = "last_outgoing_sync_time") var lastOutgoingSyncTime: LocalDateTime?,
    @Column(name = "last_incoming_sync_time") var lastIncomingSyncTime: LocalDateTime?,
    @Column(name = "last_outgoing_sync_toggle_on_time") var lastOutgoingSyncTimeToggleOnTime: LocalDateTime?,
    @Column(name = "last_outgoing_sync_toggle_off_time") var lastOutgoingSyncTimeToggleOffTime: LocalDateTime?,
    @Column(name = "external_company_id") var externalCompanyId: String? = null,
    @Column(name = "account_name") var accountName: String? = null,
    @Column(name = "initial_sync_started") var initialSyncStarted: Boolean = false,
    @Column(name = "incoming_contractor_sync_enabled") var incomingContractorSyncEnabled: Boolean = false,
    @Column(name = "incoming_contractor_sync_in_progress") var incomingContractorSyncInProgress: Boolean = false,
    @Column(name = "last_incoming_contractor_sync_time") var lastIncomingContractorSyncTime: LocalDateTime? = null,
    ) : AuditableBaseEntity(id)

fun List<JpaCompanyIntegration>.getIntegrationByNameAndCategory(
    name: String,
    category: PlatformCategory
): JpaCompanyIntegration? = firstOrNull {
    it.platform.name == name && it.platform.category == category
}
