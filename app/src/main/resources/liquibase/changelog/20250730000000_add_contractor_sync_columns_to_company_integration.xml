<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250730000000-1" author="vidya" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company_integration" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="company_integration" columnName="incoming_contractor_sync_enabled" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add incoming_contractor_sync_enabled column to company_integration table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="incoming_contractor_sync_enabled" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>

    <changeSet id="20250730000000-2" author="vidya" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company_integration" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="company_integration" columnName="incoming_contractor_sync_in_progress" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add incoming_contractor_sync_in_progress column to company_integration table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="incoming_contractor_sync_in_progress" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>

    <changeSet id="20250730000000-3" author="vidya" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company_integration" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="company_integration" columnName="last_incoming_contractor_sync_time" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add last_incoming_contractor_sync_time column to company_integration table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="last_incoming_contractor_sync_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
