package e2e

import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.kafka.ConfluentKafkaContainer

class KafkaContainerInitializer : ApplicationContextInitializer<ConfigurableApplicationContext> {

    private val kafka = ConfluentKafkaContainer("confluentinc/cp-kafka:7.4.0")
        .withReuse(true)
        .withEnv("KAFKA_HEAP_OPTS", "-Xmx512m -Xms256m") // Limit Kafka broker memory
        .withEnv("KAFKA_LOG_RETENTION_HOURS", "1") // Reduce log retention
        .withEnv("KAFKA_LOG_SEGMENT_BYTES", "1048576") // 1MB segments
        .withEnv("KAFKA_LOG_RETENTION_BYTES", "104857600") // 100MB total retention
        .withEnv("KAFKA_NUM_PARTITIONS", "1") // Reduce default partitions
        .withEnv("KAFKA_DEFAULT_REPLICATION_FACTOR", "1")
        .withEnv("KAFKA_OFFSETS_RETENTION_MINUTES", "60") // 1 hour offset retention

    init {
        kafka.start()
    }

    override fun initialize(configurableApplicationContext: ConfigurableApplicationContext) {
        TestPropertyValues.of(
            "kafka.bootstrap-servers=" + kafka.bootstrapServers,
            "platform.kafka.bootstrap-servers=" + kafka.bootstrapServers,
            "pigeon.client.kafka.bootstrap-servers=" + kafka.bootstrapServers)
            .applyTo(configurableApplicationContext.environment)
    }
}